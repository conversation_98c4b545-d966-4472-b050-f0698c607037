{"VTLANG_LANGUAGE_NAME": "Croatian (Hrvatski)", "VTLANG_STR_HOTKEY_LIST": "L:Jezik  F1:Help  F2:Browse  F3:ListView  F4:Localboot  F5:Tools  F6:ExMenu", "VTLANG_STR_HOTKEY_TREE": "L:Jezik  F1:Help  F2:Browse  F3:TreeView  F4:Localboot  F5:Tools  F6:ExMenu", "VTLANG_RETURN_PREVIOUS": "Return to previous menu [Esc]", "VTLANG_RETURN_PRV_NOESC": "Return to previous menu", "VTLANG_MENU_LANG": "Menu Language Select", "VTLANG_LB_SBOOT_WINDOWS": "Search and boot Windows", "VTLANG_LB_SBOOT_G4D": "Search and boot Grub4dos", "VTLANG_LB_SBOOT_HDD1": "Boot the 1st local disk", "VTLANG_LB_SBOOT_HDD2": "Boot the 2nd local disk", "VTLANG_LB_SBOOT_HDD3": "Boot the 3rd local disk", "VTLANG_LB_SBOOT_X64EFI": "Search and boot BOOTX64.EFI", "VTLANG_LB_SBOOT_IA32EFI": "Search and boot BOOTIA32.EFI", "VTLANG_LB_SBOOT_AA64EFI": "Search and boot BOOTAA64.EFI", "VTLANG_LB_SBOOT_XORBOOT": "Search and boot xorboot", "VTLANG_FILE_CHKSUM": "File checksum", "VTLANG_CHKSUM_MD5_CALC": "Calculate md5sum", "VTLANG_CHKSUM_SHA1_CALC": "Calculate sha1sum", "VTLANG_CHKSUM_SHA256_CALC": "Calculate sha256sum", "VTLANG_CHKSUM_SHA512_CALC": "Calculate sha512sum", "VTLANG_CHKSUM_MD5_CALC_CHK": "Calculate and check md5sum", "VTLANG_CHKSUM_SHA1_CALC_CHK": "Calculate and check sha1sum", "VTLANG_CHKSUM_SHA256_CALC_CHK": "Calculate and check sha256sum", "VTLANG_CHKSUM_SHA512_CALC_CHK": "Calculate and check sha512sum", "VTLANG_POWER": "Power", "VTLANG_POWER_REBOOT": "Reboot", "VTLANG_POWER_HALT": "Halt", "VTLANG_POWER_BOOT_EFIFW": "Reboot to EFI setup", "VTLANG_KEYBRD_LAYOUT": "Keyboard Layouts", "VTLANG_HWINFO": "Hardware Information", "VTLANG_RESOLUTION_CFG": "Resolution Configuration", "VTLANG_SCREEN_MODE": "Screen Display Mode", "VTLANG_SCREEN_TEXT_MODE": "Force Text Mode", "VTLANG_SCREEN_GUI_MODE": "Force Graphics Mode", "VTLANG_THEME_SELECT": "Theme Select", "VTLANG_UEFI_UTIL": "Ventoy UEFI Utilities", "VTLANG_UTIL_SHOW_EFI_DRV": "Show EFI Drivers", "VTLANG_UTIL_FIX_BLINIT_FAIL": "Fixup Windows BlinitializeLibrary Failure", "VTLANG_JSON_CHK_JSON": "Check plugin json configuration (ventoy.json)", "VTLANG_JSON_CHK_CONTROL": "Check global control plugin configuration", "VTLANG_JSON_CHK_THEME": "Check theme plugin configuration", "VTLANG_JSON_CHK_AUTOINS": "Check auto install plugin configuration", "VTLANG_JSON_CHK_PERSIST": "Check persistence plugin configuration", "VTLANG_JSON_CHK_MENU_ALIAS": "Check menu alias plugin configuration", "VTLANG_JSON_CHK_MENU_TIP": "Check menu tip plugin configuration", "VTLANG_JSON_CHK_MENU_CLASS": "Check menu class plugin configuration", "VTLANG_JSON_CHK_INJECTION": "Check injection plugin configuration", "VTLANG_JSON_CHK_AUTO_MEMDISK": "Check auto memdisk plugin configuration", "VTLANG_JSON_CHK_IMG_LIST": "Check image list plugin configuration", "VTLANG_JSON_CHK_IMG_BLIST": "Check image blacklist plugin configuration", "VTLANG_JSON_CHK_CONF_REPLACE": "Check boot conf replace plugin configuration", "VTLANG_JSON_CHK_DUD": "Check dud plugin configuration", "VTLANG_JSON_CHK_PASSWORD": "Check password plugin configuration", "VTLANG_NORMAL_MODE": "Boot in normal mode", "VTLANG_WIMBOOT_MODE": "Boot in wimboot mode", "VTLANG_GRUB2_MODE": "Boot in grub2 mode", "VTLANG_MEMDISK_MODE": "Boot in memdisk mode", "VTLANG_RET_TO_LISTVIEW": "Return to ListView", "VTLANG_RET_TO_TREEVIEW": "Return to TreeView", "VTLANG_NO_AUTOINS_SCRIPT": "Boot without auto installation template", "VTLANG_AUTOINS_USE": "Boot with", "VTLANG_NO_PERSIST": "Boot without persistence", "VTLANG_PERSIST_USE": "Boot with", "VTLANG_BROWER_RETURN": "Return", "VTLANG_ENTER_EXIT": "pritisnite tipku Enter za izlaz", "VTLANG_ENTER_REBOOT": "pritisnite tipku Enter za ponovno pokretanje", "VTLANG_ENTER_CONTINUE": "pritisnite tipku Enter za nastavak", "VTLANG_CTRL_TEMP_SET": "Temporary Control Settings", "VTLANG_WIN11_BYPASS_CHECK": "Bypass CPU/TPM/SecureBoot check when install Windows 11", "VTLANG_WIN11_BYPASS_NRO": "Bypass online account requirement when install Windows 11", "VTLANG_LINUX_REMOUNT": "Mount Ventoy partition after boot Linux", "VTLANG_SECONDARY_BOOT_MENU": "Show secondary boot menu", "MENU_STR_XXX": ""}