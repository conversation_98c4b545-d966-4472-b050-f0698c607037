#!/ventoy/busybox/sh
#************************************************************************************
# <AUTHOR> <EMAIL>
# 
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 3 of the
# License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <http://www.gnu.org/licenses/>.
# 
#************************************************************************************


###################################################################
#                                                                  #
# Step 1 : parse kernel debug parameter                            #
#                                                                  #
####################################################################

if ! [ -d /proc ]; then
    mkdir /proc
    vtrmproc='Y'
fi

mount -t proc proc /proc
export vtcmdline=$(cat /proc/cmdline)
export vtkerver=$(cat /proc/version)
umount /proc; 

if [ "$vtrmproc" = "Y" ]; then
    rm -rf /proc
fi

echo "kenel version=$vtkerver" >>$VTLOG
echo "kenel cmdline=$vtcmdline" >>$VTLOG

#break here for debug
if [ "$VTOY_BREAK_LEVEL" = "01" ] || [ "$VTOY_BREAK_LEVEL" = "11" ]; then
    sleep 5    
    echo -e "\n\n\033[32m ################################################# \033[0m"
    echo -e "\033[32m ################ VENTOY DEBUG ################### \033[0m"
    echo -e "\033[32m ################################################# \033[0m \n"
    
    if [ "$VTOY_BREAK_LEVEL" = "11" ]; then
        cat $VTLOG
    fi
    exec $BUSYBOX_PATH/sh
fi

if echo $vtcmdline | grep -q 'rdinit=/vtoy/vtoy'; then
    echo "handover to init_loop" >>$VTLOG
    rm -f /xxxx /vtoyxrc
    exec $BUSYBOX_PATH/sh $VTOY_PATH/init_loop
else
    echo "handover to init_chain" >>$VTLOG
    exec $BUSYBOX_PATH/sh $VTOY_PATH/init_chain
fi
