#!/ventoy/busybox/sh
#************************************************************************************
# <AUTHOR> <EMAIL>
# 
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 3 of the
# License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <http://www.gnu.org/licenses/>.
# 
#************************************************************************************

. /ventoy/hook/ventoy-hook-lib.sh

if is_ventoy_hook_finished; then
    exit 0
fi

vtlog "####### $0 $* ########"

VTPATH_OLD=$PATH; PATH=$BUSYBOX_PATH:$VTOY_PATH/tool:$PATH

wait_for_usb_disk_ready

vtdiskname=$(get_ventoy_disk_name)
if [ "$vtdiskname" = "unknown" ]; then
    vtlog "ventoy disk not found"
    PATH=$VTPATH_OLD
    exit 0
fi


vtoydm -i -f $VTOY_PATH/ventoy_image_map -d $vtdiskname > $VTOY_PATH/iso_file_list

vtline=$(grep '[-][-] drivers-.*\.squashfs'  $VTOY_PATH/iso_file_list)
sector=$(echo $vtline | awk '{print $(NF-1)}')
length=$(echo $vtline | awk '{print $NF}')

vtoydm -e -f $VTOY_PATH/ventoy_image_map -d $vtdiskname -s $sector -l $length -o $VTOY_PATH/driver.squashfs
mount -t squashfs $VTOY_PATH/driver.squashfs /lib/modules
modprobe dm-mod

umount /lib/modules
rm -f $VTOY_PATH/driver.squashfs

ventoy_udev_disk_common_hook "${vtdiskname#/dev/}2" "noreplace"

blkdev_num=$($VTOY_PATH/tool/dmsetup ls | grep ventoy | sed 's/.*(\([0-9][0-9]*\),.*\([0-9][0-9]*\).*/\1:\2/')
blkdev_dev=$($VTOY_PATH/tool/dmsetup ls | grep ventoy | sed 's/.*(\([0-9][0-9]*\),.*\([0-9][0-9]*\).*/\1 \2/')
vtDM=$(ventoy_find_dm_id ${blkdev_num})

if ! [ -b /dev/$vtDM ]; then
    mknod -m 0660 /dev/$vtDM b $blkdev_dev
fi

if mount /dev/$vtDM /media/ydfs; then
    vtlog "mount success"
else
    vtlog "mount failed"
fi

PATH=$VTPATH_OLD

set_ventoy_hook_finish
