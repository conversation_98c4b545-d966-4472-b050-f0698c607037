#!/ventoy/busybox/sh
#************************************************************************************
# <AUTHOR> <EMAIL>
# 
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 3 of the
# License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <http://www.gnu.org/licenses/>.
# 
#************************************************************************************

. $VTOY_PATH/hook/ventoy-os-lib.sh

$SED "/Running installer/i $BUSYBOX_PATH/sh $VTOY_PATH/hook/smoothwall/disk_hook.sh fakecdrom" -i /etc/install.rc

$SED "/cd \/sys\/block/a $BUSYBOX_PATH/sh $VTOY_PATH/hook/smoothwall/disk_hook.sh" -i /etc/config-install.rc
$SED "/wc *-l *\/tmp\/cd.list/i $BUSYBOX_PATH/sh $VTOY_PATH/hook/smoothwall/cd_list.sh" -i /etc/config-install.rc
