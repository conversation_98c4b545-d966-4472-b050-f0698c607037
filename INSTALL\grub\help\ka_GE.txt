 L - ინტერფეისის ენის არჩევა
F1 - დამხმარე ინფორმაციის ჩვენება
F2 - ლოკალური დისკის ფაილების დათვალიერება და ჩატვირთვა
F3 - ჩვენების რეჟიმებს შორის გადართვა 'ხის ხედი' <-> 'სიის ხედი'
F4 - ლოკალური დისკიდან Windows/Linux-ის ჩატვირთვა
F5 - ხელსაწყოები
F6 - მომხმარებლის მიერ შექმნილი Grub2 მენიუს ჩატვირთვა
F7 - ჩვენების რეჟიმის გადართვა 'გრაფიკული' <-> 'ტექსტური'

m/Ctrl+m - ფაილის საკონტროლო ჯამის შემოწმება (md5/sha1/sha256/sha512)
d/Ctrl+d - Memdisk რეჟიმი (განკუთვნილია მხოლოდ პატარა ზომის WinPE/LiveCD ISO/IMG დისტრიბუტივებისთვის)
w/Ctrl+w - WIMBOOT რეჟიმი (განკუთვნილია მხოლოდ სტანდარტული Windows/WinPE ISO დისტრიბუტივებისთვის)
r/Ctrl+r - Grub2 რეჟიმი (განკუთვნილია მხოლოდ ზოგიერთი Linux დისტრიბუტივებისთვის)
i/Ctrl+i - თავსებადი რეჟიმი (განკუთვნილია მხოლოდ გამართვისთვის)
u/Ctrl+u - ISO EFI დრაივერის ჩატვირთვა (განკუთვნილია მხოლოდ გამართვისთვის, დაუშვებელია ოფიციალური გამოყენება)



დააჭირეთ ESC დაბრუნებისთვის ......
