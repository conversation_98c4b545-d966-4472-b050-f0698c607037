{"VTLANG_LANGUAGE_NAME": "Indonesian (Bahasa Indonesia)", "VTLANG_STR_HOTKEY_LIST": "L:Bahasa  F1:Bantuan  F2:Jelajah  F3:ListView  F4:boot lokal  F5:Alat Utilitas  F6:<PERSON><PERSON>", "VTLANG_STR_HOTKEY_TREE": "L:Bahasa  F1:Bantuan  F2:Jelajah  F3:ListView  F4:boot lokal  F5:Alat Utilitas  F6:<PERSON><PERSON>", "VTLANG_RETURN_PREVIOUS": "Kembali ke menu sebelumnya [Esc]", "VTLANG_RETURN_PRV_NOESC": "Kembali ke menu sebelumnya", "VTLANG_MENU_LANG": "<PERSON><PERSON>", "VTLANG_LB_SBOOT_WINDOWS": "<PERSON><PERSON>i dan jalankan boot ke Windows", "VTLANG_LB_SBOOT_G4D": "<PERSON><PERSON><PERSON> dan jalankan boot ke Grub4dos", "VTLANG_LB_SBOOT_HDD1": "Jalankan boot ke disk lokal 1", "VTLANG_LB_SBOOT_HDD2": "Jalankan boot ke disk lokal 2", "VTLANG_LB_SBOOT_HDD3": "Jalankan boot ke disk lokal 3", "VTLANG_LB_SBOOT_X64EFI": "Jalankan boot ke disk lokal nomor 1 BOOTX64.EFI", "VTLANG_LB_SBOOT_IA32EFI": "Mencari dan jalankan boot ke BOOTIA32.EFI", "VTLANG_LB_SBOOT_AA64EFI": "Men<PERSON>i dan jalankan boot ke BOOTAA64.EFI", "VTLANG_LB_SBOOT_XORBOOT": "<PERSON><PERSON>i dan jalankan boot ke xorboot", "VTLANG_FILE_CHKSUM": "<PERSON><PERSON><PERSON> checksum", "VTLANG_CHKSUM_MD5_CALC": "Menghitung md5sum", "VTLANG_CHKSUM_SHA1_CALC": "Menghitung sha1sum", "VTLANG_CHKSUM_SHA256_CALC": "Menghitung sha256sum", "VTLANG_CHKSUM_SHA512_CALC": "Menghitung sha512sum", "VTLANG_CHKSUM_MD5_CALC_CHK": "Menghitung dan memeriksa md5sum", "VTLANG_CHKSUM_SHA1_CALC_CHK": "Menghitung dan memeriksa sha1sum", "VTLANG_CHKSUM_SHA256_CALC_CHK": "Menghitung dan memeriksa sha256sum", "VTLANG_CHKSUM_SHA512_CALC_CHK": "Menghitung dan memeriksa sha512sum", "VTLANG_POWER": "<PERSON><PERSON>", "VTLANG_POWER_REBOOT": "<PERSON><PERSON><PERSON>", "VTLANG_POWER_HALT": "<PERSON><PERSON><PERSON><PERSON>", "VTLANG_POWER_BOOT_EFIFW": "<PERSON><PERSON><PERSON>ju EFI setup", "VTLANG_KEYBRD_LAYOUT": "Tata letak Keyboard", "VTLANG_HWINFO": "Informasi Perangkat Keras", "VTLANG_RESOLUTION_CFG": "<PERSON><PERSON><PERSON>", "VTLANG_SCREEN_MODE": "Mode Tampilan <PERSON>", "VTLANG_SCREEN_TEXT_MODE": "Paksa ke Mode Teks", "VTLANG_SCREEN_GUI_MODE": "Paksa ke Mode Grafis", "VTLANG_THEME_SELECT": "<PERSON><PERSON><PERSON>", "VTLANG_UEFI_UTIL": "Ventoy UEFI Utilities", "VTLANG_UTIL_SHOW_EFI_DRV": "<PERSON><PERSON><PERSON><PERSON> Driver EFI", "VTLANG_UTIL_FIX_BLINIT_FAIL": "Memperbaiki Kegagalan Windows BlinitializeLibrary", "VTLANG_JSON_CHK_JSON": "<PERSON><PERSON><PERSON>elan plugin (ventoy.json)", "VTLANG_JSON_CHK_CONTROL": "Periksa setelan plugin global control", "VTLANG_JSON_CHK_THEME": "<PERSON><PERSON><PERSON> setelan plugin theme", "VTLANG_JSON_CHK_AUTOINS": "Periksa setelan plugin auto install", "VTLANG_JSON_CHK_PERSIST": "Periksa setelan plugin  persistence", "VTLANG_JSON_CHK_MENU_ALIAS": "<PERSON><PERSON><PERSON> setelan plugin menu alias", "VTLANG_JSON_CHK_MENU_TIP": "Periksa setelan plugin tip", "VTLANG_JSON_CHK_MENU_CLASS": "Periksa setelan class plugin menu", "VTLANG_JSON_CHK_INJECTION": "Periksa setelan plugin injection", "VTLANG_JSON_CHK_AUTO_MEMDISK": "Periksa setelan plugin auto memdisk", "VTLANG_JSON_CHK_IMG_LIST": "Periksa setelan plugin image list", "VTLANG_JSON_CHK_IMG_BLIST": "Periksa setelant plugin image blacklis", "VTLANG_JSON_CHK_CONF_REPLACE": "Periksa setelan plugin boot conf replace", "VTLANG_JSON_CHK_DUD": "<PERSON><PERSON><PERSON> setelan plugin dud", "VTLANG_JSON_CHK_PASSWORD": "<PERSON><PERSON><PERSON>n plugin password", "VTLANG_NORMAL_MODE": "Jalankan boot di mode normal", "VTLANG_WIMBOOT_MODE": "Jalankan boot di mode wimboot", "VTLANG_GRUB2_MODE": "Jalankan boot di mode grub2", "VTLANG_MEMDISK_MODE": "Jalankan boot di mode memdisk", "VTLANG_RET_TO_LISTVIEW": "Kembali ke ListView", "VTLANG_RET_TO_TREEVIEW": "Kembali ke TreeView", "VTLANG_NO_AUTOINS_SCRIPT": "Jalankan boot tanpa templat instalasi otomatis", "VTLANG_AUTOINS_USE": "Jalankan boot dengan", "VTLANG_NO_PERSIST": "Jalankan boot tanpa persistence", "VTLANG_PERSIST_USE": "Jalankan boot", "VTLANG_BROWER_RETURN": "Kembali", "VTLANG_ENTER_EXIT": "tekan tombol Enter untuk keluar", "VTLANG_ENTER_REBOOT": "tekan tombol Enter untuk reboot", "VTLANG_ENTER_CONTINUE": "tekan tombol Enter untuk melanjutkan", "VTLANG_CTRL_TEMP_SET": "Temporary Control Settings", "VTLANG_WIN11_BYPASS_CHECK": "Bypass CPU/TPM/SecureBoot check when install Windows 11", "VTLANG_WIN11_BYPASS_NRO": "Bypass online account requirement when install Windows 11", "VTLANG_LINUX_REMOUNT": "Mount Ventoy partition after boot Linux", "VTLANG_SECONDARY_BOOT_MENU": "Show secondary boot menu", "MENU_STR_XXX": ""}