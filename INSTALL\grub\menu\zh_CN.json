{"VTLANG_LANGUAGE_NAME": "Chinese Simplified (简体中文)", "VTLANG_STR_HOTKEY_LIST": "L:语言  F1:帮助  F2:本地浏览  F3:列表模式  F4:本地启动  F5:工具  F6:扩展菜单", "VTLANG_STR_HOTKEY_TREE": "L:语言  F1:帮助  F2:本地浏览  F3:树形模式  F4:本地启动  F5:工具  F6:扩展菜单", "VTLANG_RETURN_PREVIOUS": "返回上一级菜单 [Esc]", "VTLANG_RETURN_PRV_NOESC": "返回上一级菜单", "VTLANG_MENU_LANG": "菜单语言", "VTLANG_LB_SBOOT_WINDOWS": "搜索并启动 Windows", "VTLANG_LB_SBOOT_G4D": "搜索并启动 Grub4dos", "VTLANG_LB_SBOOT_HDD1": "启动本地硬盘1中的系统", "VTLANG_LB_SBOOT_HDD2": "启动本地硬盘2中的系统", "VTLANG_LB_SBOOT_HDD3": "启动本地硬盘3中的系统", "VTLANG_LB_SBOOT_X64EFI": "搜索并启动 BOOTX64.EFI", "VTLANG_LB_SBOOT_IA32EFI": "搜索并启动 BOOTIA32.EFI", "VTLANG_LB_SBOOT_AA64EFI": "搜索并启动 BOOTAA64.EFI", "VTLANG_LB_SBOOT_XORBOOT": "搜索并启动 xorboot", "VTLANG_FILE_CHKSUM": "文件校验", "VTLANG_CHKSUM_MD5_CALC": "计算 MD5 校验值", "VTLANG_CHKSUM_SHA1_CALC": "计算 SHA1 校验值", "VTLANG_CHKSUM_SHA256_CALC": "计算 SHA256 校验值", "VTLANG_CHKSUM_SHA512_CALC": "计算 SHA512 校验值", "VTLANG_CHKSUM_MD5_CALC_CHK": "计算并检查 MD5 校验值", "VTLANG_CHKSUM_SHA1_CALC_CHK": "计算并检查 SHA1 校验值", "VTLANG_CHKSUM_SHA256_CALC_CHK": "计算并检查 SHA256 校验值", "VTLANG_CHKSUM_SHA512_CALC_CHK": "计算并检查 SHA512 校验值", "VTLANG_POWER": "电源", "VTLANG_POWER_REBOOT": "重启", "VTLANG_POWER_HALT": "关机", "VTLANG_POWER_BOOT_EFIFW": "重启进入 UEFI 设置菜单", "VTLANG_KEYBRD_LAYOUT": "键盘布局", "VTLANG_HWINFO": "硬件信息", "VTLANG_RESOLUTION_CFG": "屏幕分辨率", "VTLANG_SCREEN_MODE": "显示模式", "VTLANG_SCREEN_TEXT_MODE": "文本模式", "VTLANG_SCREEN_GUI_MODE": "图形模式", "VTLANG_THEME_SELECT": "主题选择", "VTLANG_UEFI_UTIL": "Ventoy UEFI 工具", "VTLANG_UTIL_SHOW_EFI_DRV": "显示 UEFI 驱动", "VTLANG_UTIL_FIX_BLINIT_FAIL": "修复 Windows BlinitializeLibrary 错误", "VTLANG_JSON_CHK_JSON": "检查插件配置文件 (ventoy.json)", "VTLANG_JSON_CHK_CONTROL": "配置检查 —— 全局控制插件", "VTLANG_JSON_CHK_THEME": "配置检查 —— 主题插件", "VTLANG_JSON_CHK_AUTOINS": "配置检查 —— 自动安装插件", "VTLANG_JSON_CHK_PERSIST": "配置检查 —— 持久化插件", "VTLANG_JSON_CHK_MENU_ALIAS": "配置检查 —— 菜单别名插件", "VTLANG_JSON_CHK_MENU_TIP": "配置检查 —— 菜单提示插件", "VTLANG_JSON_CHK_MENU_CLASS": "配置检查 —— 菜单类型插件", "VTLANG_JSON_CHK_INJECTION": "配置检查 —— 文件注入插件", "VTLANG_JSON_CHK_AUTO_MEMDISK": "配置检查 —— 自动内存盘模式插件", "VTLANG_JSON_CHK_IMG_LIST": "配置检查 —— 文件列表插件（白名单）", "VTLANG_JSON_CHK_IMG_BLIST": "配置检查 —— 文件列表插件（黑名单）", "VTLANG_JSON_CHK_CONF_REPLACE": "配置检查 —— 启动配置替换插件", "VTLANG_JSON_CHK_DUD": "配置检查 —— Driver Update Disk插件", "VTLANG_JSON_CHK_PASSWORD": "配置检查 —— 密码插件", "VTLANG_NORMAL_MODE": "以正常模式启动", "VTLANG_WIMBOOT_MODE": "以 wimboot 模式启动", "VTLANG_GRUB2_MODE": "以 grub2 模式启动", "VTLANG_MEMDISK_MODE": "以内存盘模式启动", "VTLANG_RET_TO_LISTVIEW": "返回到列表模式", "VTLANG_RET_TO_TREEVIEW": "返回到树形模式", "VTLANG_NO_AUTOINS_SCRIPT": "不使用自动安装脚本", "VTLANG_AUTOINS_USE": "使用", "VTLANG_NO_PERSIST": "不使用持久化数据文件", "VTLANG_PERSIST_USE": "使用", "VTLANG_BROWER_RETURN": "返回", "VTLANG_ENTER_EXIT": "按回车键退出", "VTLANG_ENTER_REBOOT": "按回车键重启", "VTLANG_ENTER_CONTINUE": "按回车键继续", "VTLANG_CTRL_TEMP_SET": "控制变量设置", "VTLANG_WIN11_BYPASS_CHECK": "安装 Windows 11 时绕过 TPM/安全启动 等硬件检查", "VTLANG_WIN11_BYPASS_NRO": "安装 Windows 11 时绕过在线账户的要求", "VTLANG_LINUX_REMOUNT": "启动 Linux 系统后挂载 Ventoy 分区", "VTLANG_SECONDARY_BOOT_MENU": "显示二级启动菜单", "MENU_STR_XXX": ""}