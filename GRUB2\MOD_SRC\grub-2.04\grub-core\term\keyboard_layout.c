
#define ventoy_keyboard_set_layout(name) if (grub_strcmp(layout, #name) == 0) return ventoy_keyboard_layout_##name()

static void ventoy_keyboard_layout_QWERTY_USA(void) {
    grub_keymap_reset();
    grub_keymap_disable();
}
static void ventoy_keyboard_layout_AZERTY(void) {
grub_keymap_reset();
grub_keymap_add_by_string("a", "q"); 
grub_keymap_add_by_string("A", "Q"); 
grub_keymap_add_by_string("z", "w"); 
grub_keymap_add_by_string("Z", "W"); 
grub_keymap_add_by_string("q", "a"); 
grub_keymap_add_by_string("Q", "A"); 
grub_keymap_add_by_string("m", "semicolon"); 
grub_keymap_add_by_string("M", "colon"); 
grub_keymap_add_by_string("w", "z"); 
grub_keymap_add_by_string("W", "Z"); 
grub_keymap_add_by_string("comma", "m"); 
grub_keymap_add_by_string("question", "M"); 
grub_keymap_add_by_string("semicolon", "comma"); 
grub_keymap_add_by_string("period", "less"); 
grub_keymap_add_by_string("colon", "period"); 
grub_keymap_add_by_string("slash", "greater"); 
grub_keymap_add_by_string("exclam", "slash"); 
grub_keymap_add_by_string("dollar", "bracketright"); 
grub_keymap_add_by_string("asterisk", "backslash"); 
grub_keymap_add_by_string("percent", "doublequote"); 
grub_keymap_add_by_string("ampersand", "1"); 
grub_keymap_add_by_string("1", "exclam"); 
grub_keymap_add_by_string("tilde", "2"); 
grub_keymap_add_by_string("2", "at"); 
grub_keymap_add_by_string("doublequote", "3"); 
grub_keymap_add_by_string("3", "numbersign"); 
grub_keymap_add_by_string("quote", "4"); 
grub_keymap_add_by_string("4", "dollar"); 
grub_keymap_add_by_string("parenleft", "5"); 
grub_keymap_add_by_string("5", "percent"); 
grub_keymap_add_by_string("minus", "6"); 
grub_keymap_add_by_string("6", "caret"); 
grub_keymap_add_by_string("backquote", "7"); 
grub_keymap_add_by_string("7", "ampersand"); 
grub_keymap_add_by_string("underscore", "8"); 
grub_keymap_add_by_string("8", "asterisk"); 
grub_keymap_add_by_string("caret", "9"); 
grub_keymap_add_by_string("9", "parenleft"); 
grub_keymap_add_by_string("at", "0"); 
grub_keymap_add_by_string("0", "parenright"); 
grub_keymap_add_by_string("parenright", "minus"); 
grub_keymap_add_by_string("less", "backquote"); 
grub_keymap_add_by_string("greater", "tilde"); 
grub_keymap_add_by_string("numbersign", "braceright"); 
grub_keymap_add_by_string("backslash", "question"); 
grub_keymap_add_by_string("bracketright", "braceleft"); 
grub_keymap_add_by_string("braceleft", "quote"); 
grub_keymap_add_by_string("braceright", "underscore"); 
grub_keymap_enable();
}
static void ventoy_keyboard_layout_CZECH_QWERTY(void) {
grub_keymap_reset();
grub_keymap_add_by_string("semicolon", "backquote");
grub_keymap_add_by_string("plus", "1");
grub_keymap_add_by_string("equal", "minus");
grub_keymap_add_by_string("quote", "equal");
grub_keymap_add_by_string("parenright", "bracketright");
grub_keymap_add_by_string("doublequote", "backslash");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("1", "exclam");
grub_keymap_add_by_string("2", "at");
grub_keymap_add_by_string("3", "numbersign");
grub_keymap_add_by_string("4", "dollar");
grub_keymap_add_by_string("5", "percent");
grub_keymap_add_by_string("6", "caret");
grub_keymap_add_by_string("7", "ampersand");
grub_keymap_add_by_string("8", "asterisk");
grub_keymap_add_by_string("9", "parenleft");
grub_keymap_add_by_string("0", "parenright");
grub_keymap_add_by_string("percent", "underscore");
grub_keymap_add_by_string("slash", "braceleft");
grub_keymap_add_by_string("parenleft", "braceright");
grub_keymap_add_by_string("doublequote", "colon");
grub_keymap_add_by_string("exclam", "doublequote");
grub_keymap_add_by_string("quote", "bar");
grub_keymap_add_by_string("question", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("backquote", "Abackquote");
grub_keymap_add_by_string("exclam", "A1");
grub_keymap_add_by_string("at", "A2");
grub_keymap_add_by_string("numbersign", "A3");
grub_keymap_add_by_string("dollar", "A4");
grub_keymap_add_by_string("percent", "A5");
grub_keymap_add_by_string("caret", "A6");
grub_keymap_add_by_string("ampersand", "A7");
grub_keymap_add_by_string("asterisk", "A8");
grub_keymap_add_by_string("parenleft", "A9");
grub_keymap_add_by_string("parenright", "A0");
grub_keymap_add_by_string("minus", "Aminus");
grub_keymap_add_by_string("equal", "Aequal");
grub_keymap_add_by_string("bracketleft", "Abracketleft");
grub_keymap_add_by_string("bracketright", "Abracketright");
grub_keymap_add_by_string("semicolon", "Asemicolon");
grub_keymap_add_by_string("backslash", "Abackslash");
grub_keymap_add_by_string("less", "Acomma");
grub_keymap_add_by_string("greater", "Aperiod");
grub_keymap_add_by_string("slash", "Aslash");
grub_keymap_add_by_string("tilde", "Atilde");
grub_keymap_add_by_string("underscore", "Aunderscore");
grub_keymap_add_by_string("plus", "Aplus");
grub_keymap_add_by_string("braceleft", "Abraceleft");
grub_keymap_add_by_string("braceright", "Abraceright");
grub_keymap_add_by_string("caret", "Adoublequote");
grub_keymap_add_by_string("colon", "Acolon");
grub_keymap_add_by_string("question", "Aquestion");
grub_keymap_add_by_string("bar", "Abar");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_CZECH_QWERTZ(void) {
grub_keymap_reset();
grub_keymap_add_by_string("y", "z"); 
grub_keymap_add_by_string("z", "y"); 
grub_keymap_add_by_string("Y", "Z"); 
grub_keymap_add_by_string("Z", "Y");
grub_keymap_add_by_string("semicolon", "backquote");
grub_keymap_add_by_string("plus", "1");
grub_keymap_add_by_string("equal", "minus");
grub_keymap_add_by_string("quote", "equal");
grub_keymap_add_by_string("parenright", "bracketright");
grub_keymap_add_by_string("doublequote", "backslash");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("1", "exclam");
grub_keymap_add_by_string("2", "at");
grub_keymap_add_by_string("3", "numbersign");
grub_keymap_add_by_string("4", "dollar");
grub_keymap_add_by_string("5", "percent");
grub_keymap_add_by_string("6", "caret");
grub_keymap_add_by_string("7", "ampersand");
grub_keymap_add_by_string("8", "asterisk");
grub_keymap_add_by_string("9", "parenleft");
grub_keymap_add_by_string("0", "parenright");
grub_keymap_add_by_string("percent", "underscore");
grub_keymap_add_by_string("slash", "braceleft");
grub_keymap_add_by_string("parenleft", "braceright");
grub_keymap_add_by_string("doublequote", "colon");
grub_keymap_add_by_string("exclam", "doublequote");
grub_keymap_add_by_string("quote", "bar");
grub_keymap_add_by_string("question", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("tilde", "A1");
grub_keymap_add_by_string("caret", "A3");
grub_keymap_add_by_string("backslash", "Aq");
grub_keymap_add_by_string("bar", "Aw");
grub_keymap_add_by_string("bracketleft", "Af");
grub_keymap_add_by_string("bracketright", "Ag");
grub_keymap_add_by_string("dollar", "Asemicolon");
grub_keymap_add_by_string("numbersign", "Ax");
grub_keymap_add_by_string("ampersand", "Ac");
grub_keymap_add_by_string("at", "Av");
grub_keymap_add_by_string("braceleft", "Ab");
grub_keymap_add_by_string("braceright", "An");
grub_keymap_add_by_string("less", "Acomma");
grub_keymap_add_by_string("greater", "Aperiod");
grub_keymap_add_by_string("asterisk", "Aslash");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_DANISH(void) {
grub_keymap_reset();
grub_keymap_add_by_string("plus", "minus");
grub_keymap_add_by_string("quote", "equal");
grub_keymap_add_by_string("doublequote", "bracketright");
grub_keymap_add_by_string("quote", "backslash");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("caret", "braceright");
grub_keymap_add_by_string("asterisk", "bar");
grub_keymap_add_by_string("backquote", "plus");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("bar", "Atilde");
grub_keymap_add_by_string("backslash", "Abackquote");
grub_keymap_add_by_string("greater", "tilde");
grub_keymap_add_by_string("at", "A2");
grub_keymap_add_by_string("dollar", "A4");
grub_keymap_add_by_string("braceleft", "A7");
grub_keymap_add_by_string("bracketleft", "A8");
grub_keymap_add_by_string("bracketright", "A9");
grub_keymap_add_by_string("braceright", "A0");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("less", "quote");
grub_keymap_add_by_string("greater", "doublequote");
grub_keymap_add_by_string("tilde", "Abracketright");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_DVORAK_USA(void) {
grub_keymap_reset();
grub_keymap_add_by_string("[", "minus");
grub_keymap_add_by_string("braceleft", "underscore");
grub_keymap_add_by_string("quote", "q"); 
grub_keymap_add_by_string("doublequote", "Q"); 
grub_keymap_add_by_string("comma", "w"); 
grub_keymap_add_by_string("less", "W"); 
grub_keymap_add_by_string("s", "semicolon"); 
grub_keymap_add_by_string("S", "colon"); 
grub_keymap_add_by_string("semicolon", "z"); 
grub_keymap_add_by_string("colon", "Z"); 
grub_keymap_add_by_string("w", "comma"); 
grub_keymap_add_by_string("W", "less"); 
grub_keymap_add_by_string("v", "period"); 
grub_keymap_add_by_string("z", "greater"); 
grub_keymap_add_by_string("z", "slash"); 
grub_keymap_add_by_string("equal", "bracketright"); 
grub_keymap_add_by_string("backslash", "backslash"); 
grub_keymap_add_by_string("underscore", "doublequote"); 
grub_keymap_add_by_string("quote", "q");
grub_keymap_add_by_string("doublequote", "Q");
grub_keymap_add_by_string("comma", "w");
grub_keymap_add_by_string("less", "W");
grub_keymap_add_by_string("period", "e");
grub_keymap_add_by_string("greater", "E");
grub_keymap_add_by_string("p", "r");
grub_keymap_add_by_string("P", "R");
grub_keymap_add_by_string("y", "t");
grub_keymap_add_by_string("Y", "T");
grub_keymap_add_by_string("f", "y");
grub_keymap_add_by_string("F", "Y");
grub_keymap_add_by_string("g", "u");
grub_keymap_add_by_string("G", "U");
grub_keymap_add_by_string("c", "c");
grub_keymap_add_by_string("C", "I");
grub_keymap_add_by_string("r", "o");
grub_keymap_add_by_string("R", "O");
grub_keymap_add_by_string("l", "p");
grub_keymap_add_by_string("L", "P");
grub_keymap_add_by_string("bracketright", "equal");
grub_keymap_add_by_string("braceright", "plus");
grub_keymap_add_by_string("a", "a");
grub_keymap_add_by_string("A", "A");
grub_keymap_add_by_string("o", "s");
grub_keymap_add_by_string("O", "S");
grub_keymap_add_by_string("e", "d");
grub_keymap_add_by_string("E", "D");
grub_keymap_add_by_string("u", "f");
grub_keymap_add_by_string("U", "F");
grub_keymap_add_by_string("i", "g");
grub_keymap_add_by_string("I", "G");
grub_keymap_add_by_string("d", "h");
grub_keymap_add_by_string("D", "H");
grub_keymap_add_by_string("h", "j");
grub_keymap_add_by_string("H", "J");
grub_keymap_add_by_string("t", "k");
grub_keymap_add_by_string("T", "K");
grub_keymap_add_by_string("n", "l");
grub_keymap_add_by_string("N", "L");
grub_keymap_add_by_string("s", "semicolon");
grub_keymap_add_by_string("S", "colon");
grub_keymap_add_by_string("minus", "quote");
grub_keymap_add_by_string("underscore", "doublequote");
grub_keymap_add_by_string("semicolon", "z");
grub_keymap_add_by_string("colon", "Z");
grub_keymap_add_by_string("q", "x");
grub_keymap_add_by_string("Q", "X");
grub_keymap_add_by_string("j", "c");
grub_keymap_add_by_string("J", "C");
grub_keymap_add_by_string("k", "v");
grub_keymap_add_by_string("K", "V");
grub_keymap_add_by_string("x", "b");
grub_keymap_add_by_string("X", "B");
grub_keymap_add_by_string("b", "n");
grub_keymap_add_by_string("B", "N");
grub_keymap_add_by_string("w", "comma");
grub_keymap_add_by_string("W", "less");
grub_keymap_add_by_string("v", "period");
grub_keymap_add_by_string("V", "greater");
grub_keymap_add_by_string("z", "slash");
grub_keymap_add_by_string("Z", "question");
grub_keymap_add_by_string("slash", "bracketleft");
grub_keymap_add_by_string("question", "braceleft");
grub_keymap_add_by_string("equal", "bracketright");
grub_keymap_add_by_string("plus", "braceright");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_FRENCH(void) {
grub_keymap_reset();
grub_keymap_add_by_string("less", "backquote");
grub_keymap_add_by_string("greater", "tilde");
grub_keymap_add_by_string("ampersand", "1");
grub_keymap_add_by_string("1", "exclam");
grub_keymap_add_by_string("tilde", "2");
grub_keymap_add_by_string("2", "at");
grub_keymap_add_by_string("doublequote", "3");
grub_keymap_add_by_string("3", "numbersign");
grub_keymap_add_by_string("quote", "4");
grub_keymap_add_by_string("4", "dollar");
grub_keymap_add_by_string("parenleft", "5");
grub_keymap_add_by_string("5", "percent");
grub_keymap_add_by_string("minus", "6");
grub_keymap_add_by_string("6", "caret");
grub_keymap_add_by_string("backquote", "7");
grub_keymap_add_by_string("7", "ampersand");
grub_keymap_add_by_string("underscore", "8");
grub_keymap_add_by_string("8", "asterisk");
grub_keymap_add_by_string("backslash", "9");
grub_keymap_add_by_string("9", "parenleft");
grub_keymap_add_by_string("at", "0");
grub_keymap_add_by_string("0", "parenright");
grub_keymap_add_by_string("parenright", "minus");
grub_keymap_add_by_string("numbersign", "underscore");
grub_keymap_add_by_string("a", "q");
grub_keymap_add_by_string("A", "Q");
grub_keymap_add_by_string("z", "w");
grub_keymap_add_by_string("Z", "W");
grub_keymap_add_by_string("caret", "bracketleft");
grub_keymap_add_by_string("dollar", "bracketright");
grub_keymap_add_by_string("q", "a");
grub_keymap_add_by_string("Q", "A");
grub_keymap_add_by_string("m", "semicolon");
grub_keymap_add_by_string("M", "colon");
grub_keymap_add_by_string("bracketleft", "quote");
grub_keymap_add_by_string("percent", "doublequote");
grub_keymap_add_by_string("asterisk", "backslash");
grub_keymap_add_by_string("bracketright", "bar");
grub_keymap_add_by_string("w", "z");
grub_keymap_add_by_string("W", "Z");
grub_keymap_add_by_string("comma", "m");
grub_keymap_add_by_string("question", "M");
grub_keymap_add_by_string("semicolon", "comma");
grub_keymap_add_by_string("period", "less");
grub_keymap_add_by_string("colon", "period");
grub_keymap_add_by_string("slash", "greater");
grub_keymap_add_by_string("exclam", "slash");
grub_keymap_add_by_string("bar", "question");
grub_keymap_add_by_string("tilde", "A2");
grub_keymap_add_by_string("numbersign", "A3");
grub_keymap_add_by_string("braceleft", "A4");
grub_keymap_add_by_string("bracketleft", "A5");
grub_keymap_add_by_string("bar", "A6");
grub_keymap_add_by_string("quote", "A7");
grub_keymap_add_by_string("backslash", "A8");
grub_keymap_add_by_string("caret", "A9");
grub_keymap_add_by_string("at", "A0");
grub_keymap_add_by_string("bracketright", "Aminus");
grub_keymap_add_by_string("braceright", "Aequal");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_GERMAN(void) {
grub_keymap_reset();
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("backslash", "minus");
grub_keymap_add_by_string("z", "y");
grub_keymap_add_by_string("Z", "Y");
grub_keymap_add_by_string("y", "z");
grub_keymap_add_by_string("Y", "Z");
grub_keymap_add_by_string("plus", "bracketright");
grub_keymap_add_by_string("asterisk", "braceright");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("caret", "backquote");
grub_keymap_add_by_string("quote", "equal");
grub_keymap_add_by_string("backquote", "plus");
grub_keymap_add_by_string("braceright", "doublequote");
grub_keymap_add_by_string("bar", "bracketleft");
grub_keymap_add_by_string("at", "braceleft");
grub_keymap_add_by_string("numbersign", "backslash");
grub_keymap_add_by_string("at", "Aq");
grub_keymap_add_by_string("less", "backquote");
grub_keymap_add_by_string("greater", "tilde");
grub_keymap_add_by_string("braceleft", "A7");
grub_keymap_add_by_string("bracketleft", "A8");
grub_keymap_add_by_string("bracketright", "A9");
grub_keymap_add_by_string("braceright", "A0");
grub_keymap_add_by_string("tilde", "Abracketright");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("quote", "bar");
grub_keymap_add_by_string("greater", "semicolon");
grub_keymap_add_by_string("less", "colon");
grub_keymap_add_by_string("bar", "quote");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_ITALIANO(void) {
grub_keymap_reset();
grub_keymap_add_by_string("backslash", "backquote");
grub_keymap_add_by_string("bar", "tilde");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("tilde", "numbersign");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("quote", "minus");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("caret", "plus");
grub_keymap_add_by_string("bracketleft", "bracketleft");
grub_keymap_add_by_string("bracketright", "braceleft");
grub_keymap_add_by_string("plus", "bracketright");
grub_keymap_add_by_string("asterisk", "braceright");
grub_keymap_add_by_string("at", "semicolon");
grub_keymap_add_by_string("braceleft", "colon");
grub_keymap_add_by_string("numbersign", "quote");
grub_keymap_add_by_string("braceright", "doublequote");
grub_keymap_add_by_string("less", "backslash");
grub_keymap_add_by_string("greater", "bar");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("bracketleft", "Abracketleft");
grub_keymap_add_by_string("bracketright", "Abracketright");
grub_keymap_add_by_string("at", "Asemicolon");
grub_keymap_add_by_string("numbersign", "Aquote");
grub_keymap_add_by_string("braceright", "Abraceright");
grub_keymap_add_by_string("braceleft", "Abraceleft");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_JAPAN_106(void) {
grub_keymap_reset();
grub_keymap_add_by_string("at", "bracketleft");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("quote", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("underscore", "parenright");
grub_keymap_add_by_string("equal", "underscore");
grub_keymap_add_by_string("plus", "colon");
grub_keymap_add_by_string("colon", "quote");
grub_keymap_add_by_string("asterisk", "doublequote");
grub_keymap_add_by_string("bracketleft", "bracketright");
grub_keymap_add_by_string("braceleft", "braceright");
grub_keymap_add_by_string("bracketright", "backslash");
grub_keymap_add_by_string("braceright", "bar");
grub_keymap_add_by_string("backslash", "backquote");
grub_keymap_add_by_string("tilde", "plus");
grub_keymap_add_by_string("caret", "equal");
grub_keymap_add_by_string("backquote", "braceleft");
grub_keymap_add_by_string("bar", "tilde");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_LATIN_USA(void) {
grub_keymap_reset();
grub_keymap_add_by_string("bar", "backquote");
grub_keymap_add_by_string("quote", "minus");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("backquote", "bracketleft");
grub_keymap_add_by_string("plus", "bracketright");
grub_keymap_add_by_string("braceleft", "quote");
grub_keymap_add_by_string("braceright", "backslash");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("asterisk", "braceright");
grub_keymap_add_by_string("bracketleft", "doublequote");
grub_keymap_add_by_string("bracketright", "bar");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("caret", "Aquote");
grub_keymap_add_by_string("doublequote", "braceleft");
grub_keymap_add_by_string("at", "Aq");
grub_keymap_add_by_string("backquote", "Abackslash");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("greater", "plus");
grub_keymap_add_by_string("less", "equal");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("backquote", "Abackslash");
grub_keymap_add_by_string("tilde", "Abracketright");
grub_keymap_add_by_string("caret", "Aquote");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_PORTU_BRAZIL(void) {
grub_keymap_reset();
grub_keymap_add_by_string("quote", "backquote");
grub_keymap_add_by_string("quote", "bracketleft");
grub_keymap_add_by_string("bracketleft", "bracketright");
grub_keymap_add_by_string("tilde", "quote");
grub_keymap_add_by_string("bracketright", "backslash");
grub_keymap_add_by_string("semicolon", "slash");
grub_keymap_add_by_string("bar", "colon");
grub_keymap_add_by_string("doublequote", "tilde");
grub_keymap_add_by_string("backquote", "braceleft");
grub_keymap_add_by_string("braceleft", "braceright");
grub_keymap_add_by_string("caret", "doublequote");
grub_keymap_add_by_string("braceright", "bar");
grub_keymap_add_by_string("colon", "question");
grub_keymap_add_by_string("backslash", "semicolon");
grub_keymap_add_by_string("bar", "Atilde");
grub_keymap_add_by_string("backslash", "Abackquote");
grub_keymap_add_by_string("slash", "Aq");
grub_keymap_add_by_string("question", "Aw");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_QWERTY_UK(void) {
grub_keymap_reset();
grub_keymap_add_by_string("at", "doublequote");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("tilde", "bar");
grub_keymap_add_by_string("numbersign", "backslash");
grub_keymap_add_by_string("backslash", "numbersign");
grub_keymap_add_by_string("bar", "tilde");
grub_keymap_add_by_string("backslash", "Atilde");
grub_keymap_add_by_string("backslash", "Abackquote");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_QWERTZ(void) {
grub_keymap_reset();
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("ampersand", "percent");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("backslash", "minus");
grub_keymap_add_by_string("z", "y");
grub_keymap_add_by_string("Z", "Y");
grub_keymap_add_by_string("y", "z");
grub_keymap_add_by_string("Y", "Z");
grub_keymap_add_by_string("plus", "bracketright");
grub_keymap_add_by_string("asterisk", "braceright");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("caret", "backquote");
grub_keymap_add_by_string("backquote", "equal");
grub_keymap_add_by_string("numbersign", "backslash");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("percent", "caret"); 
grub_keymap_add_by_string("less", "numbersign"); 
grub_keymap_add_by_string("greater", "bar"); 
grub_keymap_enable();
}
static void ventoy_keyboard_layout_QWERTZ_HUN(void) {
grub_keymap_reset();
grub_keymap_add_by_string("y", "z"); 
grub_keymap_add_by_string("z", "y"); 
grub_keymap_add_by_string("Y", "Z"); 
grub_keymap_add_by_string("Z", "Y"); 
grub_keymap_add_by_string("0", "backquote");
grub_keymap_add_by_string("quote", "exclam");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("plus", "numbersign");
grub_keymap_add_by_string("exclam", "dollar");
grub_keymap_add_by_string("slash", "caret");
grub_keymap_add_by_string("equal", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk"); 
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("backslash", "Aq");
grub_keymap_add_by_string("bar", "Aw");
grub_keymap_add_by_string("bracketleft", "Af"); 
grub_keymap_add_by_string("bracketright", "Ag");
grub_keymap_add_by_string("greater", "Az");
grub_keymap_add_by_string("numbersign", "Ax");
grub_keymap_add_by_string("ampersand", "Ac");
grub_keymap_add_by_string("at", "Av");
grub_keymap_add_by_string("braceleft", "Ab"); 
grub_keymap_add_by_string("braceright", "An");
grub_keymap_add_by_string("less", "Am");
grub_keymap_add_by_string("dollar", "colon");
grub_keymap_add_by_string("question", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("at", "doublequote");
grub_keymap_add_by_string("tilde", "A1");
grub_keymap_add_by_string("caret", "A3");
grub_keymap_add_by_string("backquote", "A7");
grub_keymap_add_by_string("asterisk", "0");
grub_keymap_add_by_string("dollar", "Asemicolon");
grub_keymap_add_by_string("semicolon", "Acomma");
grub_keymap_add_by_string("greater", "Aperiod");
grub_keymap_add_by_string("asterisk", "Aslash");
grub_keymap_add_by_string("backquote", "A9");
grub_keymap_add_by_string("doublequote", "A0");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_QWERTZ_SLOV_CROAT(void) {
grub_keymap_reset();
grub_keymap_add_by_string("quote", "minus");
grub_keymap_add_by_string("plus", "equal");
grub_keymap_add_by_string("y", "z");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("doublequote", "tilde");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("asterisk", "plus");
grub_keymap_add_by_string("Y", "Z");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("tilde", "A1");
grub_keymap_add_by_string("caret", "A3");
grub_keymap_add_by_string("backquote", "A7");
grub_keymap_add_by_string("backslash", "Aq");
grub_keymap_add_by_string("bar", "Aw");
grub_keymap_add_by_string("bracketleft", "Af");
grub_keymap_add_by_string("bracketright", "Ag");
grub_keymap_add_by_string("at", "Av");
grub_keymap_add_by_string("braceleft", "Ab");
grub_keymap_add_by_string("braceright", "An");
grub_keymap_add_by_string("less", "Acomma");
grub_keymap_add_by_string("greater", "Aperiod");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_SPANISH(void) {
grub_keymap_reset();
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("caret", "braceleft");
grub_keymap_add_by_string("asterisk", "braceright");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("quote", "minus");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("greater", "bar");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("backslash", "backquote");
grub_keymap_add_by_string("less", "backslash");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("backquote", "bracketleft");
grub_keymap_add_by_string("plus", "bracketright");
grub_keymap_add_by_string("plus", "colon");
grub_keymap_add_by_string("at", "semicolon");
grub_keymap_add_by_string("bar", "A1");
grub_keymap_add_by_string("at", "A2");
grub_keymap_add_by_string("numbersign", "A3");
grub_keymap_add_by_string("tilde", "A4");
grub_keymap_add_by_string("bracketleft", "Abracketleft");
grub_keymap_add_by_string("bracketright", "Abracketright");
grub_keymap_add_by_string("braceleft", "Aquote");
grub_keymap_add_by_string("braceright", "Abackslash");
grub_keymap_add_by_string("greater", "bar");
grub_keymap_add_by_string("less", "backslash");
grub_keymap_add_by_string("backslash", "Abackquote");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_SWEDISH(void) {
grub_keymap_reset();
grub_keymap_add_by_string("plus", "minus");
grub_keymap_add_by_string("quote", "equal");
grub_keymap_add_by_string("doublequote", "bracketright");
grub_keymap_add_by_string("quote", "backslash");
grub_keymap_add_by_string("minus", "slash");
grub_keymap_add_by_string("doublequote", "at");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("caret", "braceright");
grub_keymap_add_by_string("asterisk", "bar");
grub_keymap_add_by_string("backquote", "plus");
grub_keymap_add_by_string("semicolon", "less");
grub_keymap_add_by_string("colon", "greater");
grub_keymap_add_by_string("underscore", "question");
grub_keymap_add_by_string("bar", "Atilde");
grub_keymap_add_by_string("backslash", "Abackquote");
grub_keymap_add_by_string("greater", "tilde");
grub_keymap_add_by_string("at", "A2");
grub_keymap_add_by_string("dollar", "A4");
grub_keymap_add_by_string("braceleft", "A7");
grub_keymap_add_by_string("bracketleft", "A8");
grub_keymap_add_by_string("bracketright", "A9");
grub_keymap_add_by_string("braceright", "A0");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("less", "quote");
grub_keymap_add_by_string("greater", "doublequote");
grub_keymap_add_by_string("tilde", "Abracketright");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_TURKISH_Q(void) {
grub_keymap_reset();
grub_keymap_add_by_string("doublequote", "backquote");
grub_keymap_add_by_string("asterisk", "minus");
grub_keymap_add_by_string("minus", "equal");
grub_keymap_add_by_string("comma", "backslash");
grub_keymap_add_by_string("period", "slash");
grub_keymap_add_by_string("quote", "at");
grub_keymap_add_by_string("caret", "numbersign");
grub_keymap_add_by_string("plus", "dollar");
grub_keymap_add_by_string("ampersand", "caret");
grub_keymap_add_by_string("slash", "ampersand");
grub_keymap_add_by_string("parenleft", "asterisk");
grub_keymap_add_by_string("parenright", "parenleft");
grub_keymap_add_by_string("equal", "parenright");
grub_keymap_add_by_string("question", "underscore");
grub_keymap_add_by_string("underscore", "plus");
grub_keymap_add_by_string("semicolon", "bar");
grub_keymap_add_by_string("colon", "question");
grub_keymap_add_by_string("less", "Abackquote");
grub_keymap_add_by_string("greater", "A1");
grub_keymap_add_by_string("numbersign", "A3");
grub_keymap_add_by_string("dollar", "A4");
grub_keymap_add_by_string("braceleft", "A7");
grub_keymap_add_by_string("bracketleft", "A8");
grub_keymap_add_by_string("bracketright", "A9");
grub_keymap_add_by_string("braceright", "A0");
grub_keymap_add_by_string("backslash", "Aminus");
grub_keymap_add_by_string("bar", "Aequal");
grub_keymap_add_by_string("at", "Aq");
grub_keymap_add_by_string("doublequote", "Abracketleft");
grub_keymap_add_by_string("tilde", "Abracketright");
grub_keymap_enable();
}
static void ventoy_keyboard_layout_VIETNAMESE(void) {
grub_keymap_reset();
grub_keymap_add_by_string("exclam", "A1");
grub_keymap_add_by_string("at", "A2");
grub_keymap_add_by_string("numbersign", "A3");
grub_keymap_add_by_string("dollar", "A4");
grub_keymap_add_by_string("percent", "A5");
grub_keymap_add_by_string("caret", "A6");
grub_keymap_add_by_string("ampersand", "A7");
grub_keymap_add_by_string("asterisk", "A8");
grub_keymap_add_by_string("parenleft", "A9");
grub_keymap_add_by_string("parenright", "A0");
grub_keymap_add_by_string("plus", "Aplus");
grub_keymap_add_by_string("equal", "Aequal");
grub_keymap_add_by_string("braceleft", "Abraceleft");
grub_keymap_add_by_string("braceright", "Abraceright");
grub_keymap_add_by_string("colon", "Acolon");
grub_keymap_add_by_string("semicolon", "Asemicolon");
grub_keymap_add_by_string("quote", "Aquote");
grub_keymap_add_by_string("backslash", "Abackslash");
grub_keymap_add_by_string("less", "Aless");
grub_keymap_add_by_string("greater", "Agreater");
grub_keymap_add_by_string("comma", "Acomma");
grub_keymap_add_by_string("period", "Aperiod");
grub_keymap_add_by_string("question", "Aquestion");
grub_keymap_add_by_string("slash", "Aslash");
grub_keymap_add_by_string("tilde", "Atilde");
grub_keymap_add_by_string("backquote", "Abackquote");
grub_keymap_add_by_string("bracketright", "Abracketright");
grub_keymap_add_by_string("bracketleft", "Abracketleft");
grub_keymap_add_by_string("bar", "Abar");
grub_keymap_add_by_string("doublequote", "Adoublequote");
grub_keymap_add_by_string("colon", "Acolon");
grub_keymap_add_by_string("minus", "Aminus");
grub_keymap_add_by_string("underscore", "Aunderscore");
grub_keymap_enable();
}
void ventoy_set_keyboard_layout(const char *layout);
void ventoy_set_keyboard_layout(const char *layout) {
ventoy_keyboard_set_layout(QWERTY_USA);
ventoy_keyboard_set_layout(AZERTY);
ventoy_keyboard_set_layout(CZECH_QWERTY);
ventoy_keyboard_set_layout(CZECH_QWERTZ);
ventoy_keyboard_set_layout(DANISH);
ventoy_keyboard_set_layout(DVORAK_USA);
ventoy_keyboard_set_layout(FRENCH);
ventoy_keyboard_set_layout(GERMAN);
ventoy_keyboard_set_layout(ITALIANO);
ventoy_keyboard_set_layout(JAPAN_106);
ventoy_keyboard_set_layout(LATIN_USA);
ventoy_keyboard_set_layout(PORTU_BRAZIL);
ventoy_keyboard_set_layout(QWERTY_UK);
ventoy_keyboard_set_layout(QWERTZ);
ventoy_keyboard_set_layout(QWERTZ_HUN);
ventoy_keyboard_set_layout(QWERTZ_SLOV_CROAT);
ventoy_keyboard_set_layout(SPANISH);
ventoy_keyboard_set_layout(SWEDISH);
ventoy_keyboard_set_layout(TURKISH_Q);
ventoy_keyboard_set_layout(VIETNAMESE);
}
