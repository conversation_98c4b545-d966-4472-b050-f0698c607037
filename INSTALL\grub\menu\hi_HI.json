{"VTLANG_LANGUAGE_NAME": "Hindi (हिन्दी)", "VTLANG_STR_HOTKEY_LIST": "L:भाषा  F1:मदद  F2:ब्राउज़र  F3:ListView  F4:स्थानीयबूट  F5:उपकरण  F6:ExMenu", "VTLANG_STR_HOTKEY_TREE": "L:भाषा  F1:मदद  F2:ब्राउज़र  F3:TreeView  F4:स्थानीयबूट  F5:उपकरण  F6:ExMenu", "VTLANG_RETURN_PREVIOUS": "पिछले मेनू पर लौटें [Esc]", "VTLANG_RETURN_PRV_NOESC": "पिछले मेनू पर लौटें", "VTLANG_MENU_LANG": "मेनू भाषा का चयन करें", "VTLANG_LB_SBOOT_WINDOWS": "Windows खोजें और बूट करें", "VTLANG_LB_SBOOT_G4D": "Grub4dos खोजें और बूट करें", "VTLANG_LB_SBOOT_HDD1": "पहली(1st) स्थानीय डिस्क को बूट करें", "VTLANG_LB_SBOOT_HDD2": "दूसरी(2nd) स्थानीय डिस्क बूट करें", "VTLANG_LB_SBOOT_HDD3": "तीसरी(3rd) स्थानीय डिस्क बूट करें", "VTLANG_LB_SBOOT_X64EFI": "BOOTX64.EFI खोजें और बूट करें", "VTLANG_LB_SBOOT_IA32EFI": "BOOTIA32.EFI खोजें और बूट करें", "VTLANG_LB_SBOOT_AA64EFI": "BOOTAA64.EFI खोजें और बूट करें", "VTLANG_LB_SBOOT_XORBOOT": "xorboot खोजें और बूट करें", "VTLANG_FILE_CHKSUM": "फ़ाइल चेकसम", "VTLANG_CHKSUM_MD5_CALC": "Md5sum की गणना करें", "VTLANG_CHKSUM_SHA1_CALC": "sha<PERSON><PERSON>m की गणना करें", "VTLANG_CHKSUM_SHA256_CALC": "sha256sum की गणना करें", "VTLANG_CHKSUM_SHA512_CALC": "sha512sum की गणना करें", "VTLANG_CHKSUM_MD5_CALC_CHK": "md5sum की गणना और जाँच करें", "VTLANG_CHKSUM_SHA1_CALC_CHK": "sha<PERSON>sum की गणना और जाँच करें", "VTLANG_CHKSUM_SHA256_CALC_CHK": "sha256sum की गणना और जाँच करें", "VTLANG_CHKSUM_SHA512_CALC_CHK": "sha512sum की गणना और जाँच करें", "VTLANG_POWER": "<PERSON>ा<PERSON>र", "VTLANG_POWER_REBOOT": "रीबूट", "VTLANG_POWER_HALT": "हाल्ट", "VTLANG_POWER_BOOT_EFIFW": "EFI सेटअप के लिए रीबूट करें", "VTLANG_KEYBRD_LAYOUT": "कीबोर्ड लेआउट", "VTLANG_HWINFO": "हार्डवेयर की जानकारी", "VTLANG_RESOLUTION_CFG": "रिज़ॉल्यूशन कॉन्फ़िगरेशन", "VTLANG_SCREEN_MODE": "Screen Display मोड", "VTLANG_SCREEN_TEXT_MODE": "फोर्स Text मोड", "VTLANG_SCREEN_GUI_MODE": "फोर्स Graphics मोड", "VTLANG_THEME_SELECT": "थीम का चयन", "VTLANG_UEFI_UTIL": "वेंटोय UEFI यूटिलिटीज", "VTLANG_UTIL_SHOW_EFI_DRV": "EFI ड्राइवर्स दिखाएं", "VTLANG_UTIL_FIX_BLINIT_FAIL": "Windows BlinitializeLibrary विफलता को ठीक करें", "VTLANG_JSON_CHK_JSON": "प्लगइन json विन्यास की जाँच करें (ventoy.json)", "VTLANG_JSON_CHK_CONTROL": "global control प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_THEME": "theme प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_AUTOINS": "auto install प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_PERSIST": "persistence प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_MENU_ALIAS": "menu alias प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_MENU_TIP": "menu tip प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_MENU_CLASS": "menu class प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_INJECTION": "injection प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_AUTO_MEMDISK": "auto memdisk प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_IMG_LIST": "image list प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_IMG_BLIST": "image blacklist प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_CONF_REPLACE": "boot conf replace प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_DUD": "dud प्लगइन विन्यास की जाँच करें", "VTLANG_JSON_CHK_PASSWORD": "password प्लगइन विन्यास की जाँच करें", "VTLANG_NORMAL_MODE": "सामान्य मोड में बूट करें", "VTLANG_WIMBOOT_MODE": "wimboot मोड में बूट करें", "VTLANG_GRUB2_MODE": "grub2 मोड में बूट करें", "VTLANG_MEMDISK_MODE": "memdisk मोड में बूट करें", "VTLANG_RET_TO_LISTVIEW": "ListView पर वापस जाएँ", "VTLANG_RET_TO_TREEVIEW": "Tree<PERSON>iew पर वापस जाएँ", "VTLANG_NO_AUTOINS_SCRIPT": "ऑटो इंस्टॉलेशन टेम्पलेट के बिना बूट करें", "VTLANG_AUTOINS_USE": "के साथ बूट करें", "VTLANG_NO_PERSIST": "बिना persistence के बूट करें", "VTLANG_PERSIST_USE": "के साथ बूट करें", "VTLANG_BROWER_RETURN": "लौटें", "VTLANG_ENTER_EXIT": "बाहर निकलने के लिए एंटर कुंजी दबाएं", "VTLANG_ENTER_REBOOT": "रिबूट करने के लिए एंटर कुंजी दबाएं", "VTLANG_ENTER_CONTINUE": "जारी रखने के लिए एंटर कुंजी दबाएं", "VTLANG_CTRL_TEMP_SET": "अस्थायी नियंत्रण सेटिंग्स", "VTLANG_WIN11_BYPASS_CHECK": "जब विंडोज 11 स्थापित करें, CPU/TPM/SECUREBOOT CHECK को बायपास करें", "VTLANG_WIN11_BYPASS_NRO": "विंडोज 11 स्थापित करते समय ऑनलाइन खाते की आवश्यकता बाईपास", "VTLANG_LINUX_REMOUNT": "लिनक्स बूट के बाद वेंटॉय विभाजन को माउंट करें", "VTLANG_SECONDARY_BOOT_MENU": "द्वितीयक बूट मेनू दिखाएं", "MENU_STR_XXX": ""}