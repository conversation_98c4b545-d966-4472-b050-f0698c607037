#!/ventoy/busybox/sh
#************************************************************************************
# <AUTHOR> <EMAIL>
# 
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 3 of the
# License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <http://www.gnu.org/licenses/>.
# 
#************************************************************************************

. /ventoy/hook/ventoy-hook-lib.sh

VTPATH_OLD=$PATH; PATH=$BUSYBOX_PATH:$VTOY_PATH/tool:$PATH

NEWROOT=$(grep switch_root /init | awk '{print $3}')

for i in 'usr/bin' 'usr/sbin'; do
    if [ -f $NEWROOT/$i/udevadm ]; then
        UPATH=$i
        break
    fi
done

blkdev_num=$(dmsetup ls | grep ventoy | sed 's/.*(\([0-9][0-9]*\),.*\([0-9][0-9]*\).*/\1:\2/')
vtDM=$(ventoy_find_dm_id ${blkdev_num})

sed "s#UPATH=.*#UPATH=/$UPATH#"  -i /ventoy/hook/clear/udevadm
sed "s#DM=.*#DM=$vtDM#"  -i /ventoy/hook/clear/udevadm


mv $NEWROOT/$UPATH/udevadm  $NEWROOT/$UPATH/udevadm_bk
cp -a /ventoy/hook/clear/udevadm $NEWROOT/$UPATH/udevadm
chmod 777 $NEWROOT/$UPATH/udevadm

