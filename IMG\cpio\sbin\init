#!/ventoy/busybox/ash
#************************************************************************************
# <AUTHOR> <EMAIL>
# 
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License as
# published by the Free Software Foundation; either version 3 of the
# License, or (at your option) any later version.
# 
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <http://www.gnu.org/licenses/>.
# 
#************************************************************************************

####################################################################
#                                                                  #
# Step 1 : extract busybox & set busybox environment               #
#                                                                  #
####################################################################

export VTOY_ORG_PATH=$PATH
export VTOY_PATH=/ventoy
export BUSYBOX_PATH=$VTOY_PATH/busybox
export VTLOG=$VTOY_PATH/log
export FIND=$BUSYBOX_PATH/find
export GREP=$BUSYBOX_PATH/grep
export EGREP=$BUSYBOX_PATH/egrep
export CAT=$BUSYBOX_PATH/cat
export AWK=$BUSYBOX_PATH/awk
export SED=$BUSYBOX_PATH/sed
export SLEEP=$BUSYBOX_PATH/sleep
export HEAD=$BUSYBOX_PATH/head

if [ -e $BUSYBOX_PATH/busyboxaa64.xz ]; then
    export VTOY_ARCH=aarch64
elif [ -e $BUSYBOX_PATH/busyboxm64e.xz ]; then
    export VTOY_ARCH=mips64el
else
    if [ -e $BUSYBOX_PATH/32h ]; then
        export VTOY_ARCH=x86_64
    else
        export VTOY_ARCH=i386
    fi
fi

if [ "$VTOY_ARCH" = "i386" ]; then
    if $BUSYBOX_PATH/vtchmod32 -6; then
        export VTOY_ARCH=x86_64
    fi
fi

echo $VTOY_ARCH > $VTOY_PATH/ventoy_arch


if [ "$VTOY_ARCH" = "aarch64" ]; then
    $BUSYBOX_PATH/xzminidecaa64 < $BUSYBOX_PATH/busyboxaa64.xz > $BUSYBOX_PATH/busybox
    $BUSYBOX_PATH/vtchmodaa64 $BUSYBOX_PATH/busybox
elif [ "$VTOY_ARCH" = "mips64el" ]; then
    $BUSYBOX_PATH/xzminidecm64e < $BUSYBOX_PATH/busyboxm64e.xz > $BUSYBOX_PATH/busybox
    $BUSYBOX_PATH/vtchmodm64e $BUSYBOX_PATH/busybox
elif [ "$VTOY_ARCH" = "x86_64" ]; then
    $BUSYBOX_PATH/xzminidec64 < $BUSYBOX_PATH/busybox64.xz > $BUSYBOX_PATH/busybox
    if [ -s $BUSYBOX_PATH/busybox ]; then
        $BUSYBOX_PATH/vtchmod64 $BUSYBOX_PATH/busybox
    else
        $BUSYBOX_PATH/xzminidec64_musl < $BUSYBOX_PATH/busybox64.xz > $BUSYBOX_PATH/busybox 
        $BUSYBOX_PATH/vtchmod64_musl $BUSYBOX_PATH/busybox
    fi
else
    $BUSYBOX_PATH/xzminidec32 < $BUSYBOX_PATH/busybox32.xz > $BUSYBOX_PATH/busybox
    $BUSYBOX_PATH/vtchmod32 $BUSYBOX_PATH/busybox
fi

if [ -e $BUSYBOX_PATH/busybox ]; then
    $BUSYBOX_PATH/busybox --install $BUSYBOX_PATH
else
    $BUSYBOX_PATH/tmpxz -d $BUSYBOX_PATH/busybox32.xz
    $BUSYBOX_PATH/busybox32 --install $BUSYBOX_PATH
fi

export PATH=$BUSYBOX_PATH/:$VTOY_PATH/tool

export VTOY_BREAK_LEVEL=$(hexdump -n 1 -s 449 -e '1/1 "%02x"' $VTOY_PATH/ventoy_os_param)
export VTOY_DEBUG_LEVEL=$(hexdump -n 1 -s 450 -e '1/1 "%02x"' $VTOY_PATH/ventoy_os_param)
export VTOY_LINUX_REMOUNT=$(hexdump -n 1 -s 454 -e '1/1 "%02x"' $VTOY_PATH/ventoy_os_param)

#Fixme: busybox shell output redirect seems to have some bug in rhel5
if uname -a | grep -q el5; then
    VTOY_REDT_BUG=YES
fi

if [ -z "$VTOY_REDT_BUG" ]; then
    echo "============== VENTOY =================" >>$VTLOG
fi

cd $VTOY_PATH
xz -d ventoy_chain.sh.xz
xz -d ventoy_loop.sh.xz

if [ -n "$VTOY_REDT_BUG" ]; then
    xz -d -c hook.cpio.xz | cpio -idm
    xz -d -c tool.cpio.xz | cpio -idm
    xz -d -c loop.cpio.xz | cpio -idm
else
    xz -d -c hook.cpio.xz | cpio -idm 2>>$VTLOG
    xz -d -c tool.cpio.xz | cpio -idm 2>>$VTLOG
    xz -d -c loop.cpio.xz | cpio -idm 2>>$VTLOG
fi


if [ "$VTOY_ARCH" = "x86_64" ]; then
    echo "Use x86_64 busybox toolkit ..." >>$VTLOG
    $BUSYBOX_PATH/xzcat $BUSYBOX_PATH/xzcat64_musl.xz > $BUSYBOX_PATH/xzcat_musl
    $BUSYBOX_PATH/chmod +x $BUSYBOX_PATH/xzcat_musl
    ln -s $BUSYBOX_PATH/xzminidec64 $BUSYBOX_PATH/xzminidec
    ln -s $VTOY_PATH/tool/dmsetup64 $VTOY_PATH/tool/dmsetup
    ln -s $VTOY_PATH/tool/lunzip64 $VTOY_PATH/tool/lunzip
    
    rm -f $VTOY_PATH/tool/lz4cat $VTOY_PATH/tool/zstdcat
    ln -s $VTOY_PATH/tool/lz4cat64 $VTOY_PATH/tool/lz4cat
    ln -s $VTOY_PATH/tool/zstdcat64 $VTOY_PATH/tool/zstdcat
elif [ "$VTOY_ARCH" = "i386" ]; then
    echo "Use i386 busybox toolkit ..." >>$VTLOG
    $BUSYBOX_PATH/xzcat $BUSYBOX_PATH/xzcat32_musl.xz > $BUSYBOX_PATH/xzcat_musl
    $BUSYBOX_PATH/chmod +x $BUSYBOX_PATH/xzcat_musl
    ln -s $BUSYBOX_PATH/xzminidec32 $BUSYBOX_PATH/xzminidec
    ln -s $VTOY_PATH/tool/dmsetup32 $VTOY_PATH/tool/dmsetup
    ln -s $VTOY_PATH/tool/lunzip32 $VTOY_PATH/tool/lunzip

    if uname -a | egrep -q 'x86_64|amd64'; then
        echo "zstdcat use 64bit ..." >>$VTLOG
        rm -f $VTOY_PATH/tool/zstdcat
        ln -s $VTOY_PATH/tool/zstdcat64 $VTOY_PATH/tool/zstdcat
    fi
elif [ "$VTOY_ARCH" = "mips64el" ]; then
    echo "Use MIPS64 busybox toolkit ..." >>$VTLOG
    ln -s $BUSYBOX_PATH/xzminidecm64e $BUSYBOX_PATH/xzminidec
    ln -s $VTOY_PATH/tool/dmsetupm64e $VTOY_PATH/tool/dmsetup
    
    # TBD
    #ln -s $VTOY_PATH/tool/lunzipm64e $VTOY_PATH/tool/lunzip
    
    rm -f $VTOY_PATH/tool/lz4cat $VTOY_PATH/tool/zstdcat
    ln -s $VTOY_PATH/tool/lz4catm64e $VTOY_PATH/tool/lz4cat
    
    # TBD
    #ln -s $VTOY_PATH/tool/zstdcataa64 $VTOY_PATH/tool/zstdcat

elif [ "$VTOY_ARCH" = "aarch64" ]; then
    echo "Use ARM64 busybox toolkit ..." >>$VTLOG
    ln -s $BUSYBOX_PATH/xzminidecaa64 $BUSYBOX_PATH/xzminidec
    ln -s $VTOY_PATH/tool/dmsetupaa64 $VTOY_PATH/tool/dmsetup
    ln -s $VTOY_PATH/tool/lunzipaa64 $VTOY_PATH/tool/lunzip
    
    rm -f $VTOY_PATH/tool/lz4cat $VTOY_PATH/tool/zstdcat
    ln -s $VTOY_PATH/tool/lz4cataa64 $VTOY_PATH/tool/lz4cat
    ln -s $VTOY_PATH/tool/zstdcataa64 $VTOY_PATH/tool/zstdcat
else
    echo "Unknown busybox toolkit ..." >>$VTLOG
fi

rm -f *.xz
cd /

####################################################################
#                                                                  #
# Step 2 : Hand over to ventoy init                                #
#                                                                  #
####################################################################
exec $BUSYBOX_PATH/sh $VTOY_PATH/init
