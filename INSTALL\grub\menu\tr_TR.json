{"VTLANG_LANGUAGE_NAME": "Turkish (Türkçe)", "VTLANG_STR_HOTKEY_LIST": "L:Dil  F1:Yardım  F2:Gözat  F3:Liste Görünümü  F4:<PERSON><PERSON><PERSON><PERSON><PERSON> Seçenekleri  F5:Araçlar  F6:Grub2 Menü", "VTLANG_STR_HOTKEY_TREE": "L:Dil  F1:Yardım  F2:Gözat  F3:<PERSON><PERSON><PERSON><PERSON>örünümü   F4:<PERSON><PERSON><PERSON><PERSON><PERSON> Seçenekleri  F5:Araçlar  F6:Grub2 Menü", "VTLANG_RETURN_PREVIOUS": "Önceki menüye geri dön [Esc]", "VTLANG_RETURN_PRV_NOESC": "Önceki menüye geri dön", "VTLANG_MENU_LANG": "<PERSON><PERSON>", "VTLANG_LB_SBOOT_WINDOWS": "Windows'u bul ve Önyükle", "VTLANG_LB_SBOOT_G4D": "Grub4dos'u bul ve Önyükle", "VTLANG_LB_SBOOT_HDD1": "1. <PERSON><PERSON> yap", "VTLANG_LB_SBOOT_HDD2": "2. <PERSON><PERSON> yap", "VTLANG_LB_SBOOT_HDD3": "3. <PERSON><PERSON> yap", "VTLANG_LB_SBOOT_X64EFI": "BOOTX64.EFI'yi bul ve Önyükleme yap", "VTLANG_LB_SBOOT_IA32EFI": "BOOTIA32.EFI'yi bul ve Önyükleme yap", "VTLANG_LB_SBOOT_AA64EFI": "BOOTAA64.EFI'yi bul ve Önyükleme yap", "VTLANG_LB_SBOOT_XORBOOT": "XORBOOT'u bul ve Önyükleme yap", "VTLANG_FILE_CHKSUM": "<PERSON><PERSON><PERSON>", "VTLANG_CHKSUM_MD5_CALC": "md5sum değ<PERSON>", "VTLANG_CHKSUM_SHA1_CALC": "sha<PERSON><PERSON><PERSON>", "VTLANG_CHKSUM_SHA256_CALC": "sha256<PERSON><PERSON>", "VTLANG_CHKSUM_SHA512_CALC": "sha512<PERSON>m <PERSON>", "VTLANG_CHKSUM_MD5_CALC_CHK": "md5sum hesapla ve kontrol et", "VTLANG_CHKSUM_SHA1_CALC_CHK": "sha1sum hesapla ve kontrol et", "VTLANG_CHKSUM_SHA256_CALC_CHK": "sha256sum hesapla ve kontrol et", "VTLANG_CHKSUM_SHA512_CALC_CHK": "sha512sum hesapla ve kontrol et", "VTLANG_POWER": "<PERSON><PERSON><PERSON>", "VTLANG_POWER_REBOOT": "<PERSON><PERSON><PERSON>", "VTLANG_POWER_HALT": "Bilgisayarı Kapat", "VTLANG_POWER_BOOT_EFIFW": "EFI BIOS Ayarları ile Yeniden Başlat", "VTLANG_KEYBRD_LAYOUT": "Klavye düzenleri", "VTLANG_HWINFO": "<PERSON><PERSON><PERSON><PERSON>", "VTLANG_RESOLUTION_CFG": "Çözünürlük Yapılandırması", "VTLANG_SCREEN_MODE": "Ekran Görüntü<PERSON>e <PERSON>", "VTLANG_SCREEN_TEXT_MODE": "<PERSON><PERSON>", "VTLANG_SCREEN_GUI_MODE": "<PERSON><PERSON>", "VTLANG_THEME_SELECT": "<PERSON><PERSON>", "VTLANG_UEFI_UTIL": "Ventoy UEFI Utilities", "VTLANG_UTIL_SHOW_EFI_DRV": "EFI Sürücülerini Göster", "VTLANG_UTIL_FIX_BLINIT_FAIL": "Windows BlinitializeLibrary Hatasını Düzelt", "VTLANG_JSON_CHK_JSON": "json (ventoy.json) Yapılandırma Dosyası Ayarlarını kontrol et", "VTLANG_JSON_CHK_CONTROL": "(Global Control Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_THEME": "(Theme Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_AUTOINS": "(Auto Install Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_PERSIST": "(Persistence Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_MENU_ALIAS": "(<PERSON><PERSON>) yapılandırma ayarları", "VTLANG_JSON_CHK_MENU_TIP": "(<PERSON><PERSON> Tip Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_MENU_CLASS": "(Menu Class Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_INJECTION": "(Injection Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_AUTO_MEMDISK": "(Auto Memdisk Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_IMG_LIST": "(Image List Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_IMG_BLIST": "(Image Blacklist Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_CONF_REPLACE": "(Check Boot Conf Replace Plugin) yapılandırma ayarları", "VTLANG_JSON_CHK_DUD": "(<PERSON><PERSON>) yapılandırma ayarları", "VTLANG_JSON_CHK_PASSWORD": "(Password Plugin) yapılandırma ayarları", "VTLANG_NORMAL_MODE": "NORMAL(ISO) Mod'da ÖnYükleme Yap", "VTLANG_WIMBOOT_MODE": "WIMBOOT(WIM) Mod'da Önyükleme Yap", "VTLANG_GRUB2_MODE": "GRUB2 Mod'da Önyükleme Yap", "VTLANG_MEMDISK_MODE": "MEMDISK Modunda ÖnYükleme Yap", "VTLANG_RET_TO_LISTVIEW": "Liste Görünümü Moduna <PERSON>", "VTLANG_RET_TO_TREEVIEW": "<PERSON><PERSON><PERSON>ç Görünümü Moduna Geri Dön", "VTLANG_NO_AUTOINS_SCRIPT": "Otomatik yükleme şablonu(autoinstalltemplate.xml) olmadan Önyükleme yap", "VTLANG_AUTOINS_USE": "<PERSON><PERSON>", "VTLANG_NO_PERSIST": "Kalıcı ayar dosyası(persistence.dat) o<PERSON>dan <PERSON>", "VTLANG_PERSIST_USE": "<PERSON><PERSON>", "VTLANG_BROWER_RETURN": "<PERSON><PERSON>", "VTLANG_ENTER_EXIT": "çıkmak için ENTER tuşuna basın", "VTLANG_ENTER_REBOOT": "yeniden başlatmak için ENTER'a basın", "VTLANG_ENTER_CONTINUE": "devam etmek için ENTER'a basın", "VTLANG_CTRL_TEMP_SET": "Geçici Kontrol <PERSON>ı", "VTLANG_WIN11_BYPASS_CHECK": "Windows11'i y<PERSON><PERSON><PERSON><PERSON>, TPM/CPU/RAM kontrollerini atla", "VTLANG_WIN11_BYPASS_NRO": "Windows<PERSON>'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(OOBE) atla", "VTLANG_LINUX_REMOUNT": "Linux sistemi <PERSON>, <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>", "VTLANG_SECONDARY_BOOT_MENU": "İ<PERSON><PERSON>l ö<PERSON>ükleme menüsünü gö<PERSON>", "MENU_STR_XXX": ""}