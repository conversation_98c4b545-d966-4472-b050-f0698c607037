 L - 言語を選択
F1 - この画面を表示する
F2 - 手元の記憶装置にある起動ファイルを選択して実行する
F3 - 表示形式を切り替える（一覧 ↔ 階層）
F4 - 手元の記憶装置にあるWindowsまたはLinuxを起動する
F5 - 諸機能
F6 - Grub2の構成を読み込む
F7 - 操作形式を切り替える（GUI ↔ CUI）

m/Ctrl+m - イメージの検査合計を計算する (md5/sha1/sha256/sha512)
d/Ctrl+d - 主記憶装置上に記憶域を作成する（容量の小さなWinPE・LiveCD専用）
w/Ctrl+w - WIMBOOTモード（Windows/WinPE ISO専用）
r/Ctrl+r - Grub2モード（一部のLinuxディストリビューション専用）
i/Ctrl+i - 互換モード（開発用）
u/Ctrl+u - ISO efiドライバーを読み取る（開発用，非公式）



この画面を閉じるにはESCを押してください ......
