{"VTLANG_LANGUAGE_NAME": "Georgian (ქართული)", "VTLANG_STR_HOTKEY_LIST": "L:ენა  F1:დახმარება  F2:დათვალიერება  F3:სიის ხედი  F4:ლოკალური ჩატვირთვა  F5:ხელსაწყოები  F6:გასვლა", "VTLANG_STR_HOTKEY_TREE": "L:ენა  F1:დახმარება  F2:დათვალიერება  F3:ხის ხედი   F4:ლოკალური ჩატვირთვა  F5:ხელსაწყოები  F6:გასვლა", "VTLANG_RETURN_PREVIOUS": "წინა მენიუში დაბრუნება [Esc]", "VTLANG_RETURN_PRV_NOESC": "წინა მენიუში დაბრუნება", "VTLANG_MENU_LANG": "მენიუს ენის არჩევა", "VTLANG_LB_SBOOT_WINDOWS": "Windows-ის მოძებნა და ჩატვირთვა", "VTLANG_LB_SBOOT_G4D": "Grub4dos-ის მოძებნა და ჩატვირთვა", "VTLANG_LB_SBOOT_HDD1": "1-ლი ლოკალური მყარი დისკიდან ჩატვირთვა", "VTLANG_LB_SBOOT_HDD2": "მე-2 ლოკალური მყარი დისკიდან ჩატვირთვა", "VTLANG_LB_SBOOT_HDD3": "მე-3 ლოკალური მყარი დისკიდან ჩატვირთვა", "VTLANG_LB_SBOOT_X64EFI": "BOOTX64.EFI-ის მოძებნა და ჩატვირთვა", "VTLANG_LB_SBOOT_IA32EFI": "BOOTIA32.EFI-ის მოძებნა და ჩატვირთვა", "VTLANG_LB_SBOOT_AA64EFI": "BOOTAA64.EFI-ის მოძებნა და ჩატვირთვა", "VTLANG_LB_SBOOT_XORBOOT": "xorboot-ის მოძებნა და ჩატვირთვა", "VTLANG_FILE_CHKSUM": "ფაილის საკონტროლო ჯამი", "VTLANG_CHKSUM_MD5_CALC": "md5sum გამოთვლა", "VTLANG_CHKSUM_SHA1_CALC": "sha1sum გამოთვლა", "VTLANG_CHKSUM_SHA256_CALC": "sha256sum გამოთვლა", "VTLANG_CHKSUM_SHA512_CALC": "sha512sum გამოთვლა", "VTLANG_CHKSUM_MD5_CALC_CHK": "md5sum გამოთვლა და შემოწმება", "VTLANG_CHKSUM_SHA1_CALC_CHK": "sha1sum გამოთვლა და შემოწმება", "VTLANG_CHKSUM_SHA256_CALC_CHK": "sha256sum გამოთვლა და შემოწმება", "VTLANG_CHKSUM_SHA512_CALC_CHK": "sha512sum გამოთვლა და შემოწმება", "VTLANG_POWER": "კომპიუტერის გამორთვა", "VTLANG_POWER_REBOOT": "კომპიუტერის გადატვირთვა", "VTLANG_POWER_HALT": "კომპიუტერის გაჩერება", "VTLANG_POWER_BOOT_EFIFW": "გადატვირთვა EFI კონფიგურაციის რეჟიმში", "VTLANG_KEYBRD_LAYOUT": "კლავიატურის განლაგებები", "VTLANG_HWINFO": "ინფორმაცია მოწყობილობების შესახებ", "VTLANG_RESOLUTION_CFG": "გარჩევადობის კონფიგურაცია", "VTLANG_SCREEN_MODE": "ჩვენების რეჟიმი", "VTLANG_SCREEN_TEXT_MODE": "ჩვენების ტექსტური რეჟიმი", "VTLANG_SCREEN_GUI_MODE": "ჩვენების გრაფიკული რეჟიმი", "VTLANG_THEME_SELECT": "თემის არჩევა", "VTLANG_UEFI_UTIL": "Ventoy UEFI Utilities", "VTLANG_UTIL_SHOW_EFI_DRV": "EFI დრაივერების ჩვენება", "VTLANG_UTIL_FIX_BLINIT_FAIL": "Windows BlinitializeLibrary ხარვეზის გასწორება", "VTLANG_JSON_CHK_JSON": "მოდულების json კონფიგურაციის შემოწმება (ventoy.json)", "VTLANG_JSON_CHK_CONTROL": "global control მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_THEME": "theme მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_AUTOINS": "auto install მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_PERSIST": "persistence მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_MENU_ALIAS": "menu alias მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_MENU_TIP": "menu tip მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_MENU_CLASS": "menu class მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_INJECTION": "injection მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_AUTO_MEMDISK": "auto memdisk მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_IMG_LIST": "image list მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_IMG_BLIST": "image blacklist მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_CONF_REPLACE": "boot conf replace მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_DUD": "dud მოდულის კონფიგურაციის შემოწმება", "VTLANG_JSON_CHK_PASSWORD": "password მოდულის კონფიგურაციის შემოწმება", "VTLANG_NORMAL_MODE": "ჩატვირთვა normal რეჟიმში", "VTLANG_WIMBOOT_MODE": "ჩატვირთვა wimboot რეჟიმში", "VTLANG_GRUB2_MODE": "ჩატვირთვა grub2 რეჟიმში", "VTLANG_MEMDISK_MODE": "ჩატვირთვა memdisk რეჟიმში", "VTLANG_RET_TO_LISTVIEW": "სიის ხედის დაბრუნება", "VTLANG_RET_TO_TREEVIEW": "ხის ხედის დაბრუნება", "VTLANG_NO_AUTOINS_SCRIPT": "ჩატვირთვა ავტომატური ინსტალაციის სკრიპტის გარეშე", "VTLANG_AUTOINS_USE": "ჩატვირთვა ავტომატური ინსტალაციის რეჟიმში", "VTLANG_NO_PERSIST": "ჩატვირთვა ცვლილებების შენახვის რეჟიმის გარეშე", "VTLANG_PERSIST_USE": "ჩატვირთვა ცვლილებების შენახვის რეჟიმში", "VTLANG_BROWER_RETURN": "დაბრუნება", "VTLANG_ENTER_EXIT": "გასასვლელად დააჭირეთ Enter ღილაკს", "VTLANG_ENTER_REBOOT": "დააჭირეთ Enter ღილაკს გადატვირთვისთვის", "VTLANG_ENTER_CONTINUE": "გასაგრძელებლად დააჭირეთ Enter ღილაკს", "VTLANG_CTRL_TEMP_SET": "Temporary Control Settings", "VTLANG_WIN11_BYPASS_CHECK": "Bypass CPU/TPM/SecureBoot check when install Windows 11", "VTLANG_WIN11_BYPASS_NRO": "Bypass online account requirement when install Windows 11", "VTLANG_LINUX_REMOUNT": "Mount Ventoy partition after boot Linux", "VTLANG_SECONDARY_BOOT_MENU": "Show secondary boot menu", "MENU_STR_XXX": ""}